["test_complete_system.py::test_complete_system", "test_firebase_cache.py::test_firebase_cache", "test_google_api_live.py::test_document_processor_integration", "test_google_api_live.py::test_google_api_live", "test_google_embeddings_integration.py::test_google_embeddings", "test_google_embeddings_integration.py::test_with_api_key", "test_health_endpoints.py::test_core_components", "test_health_endpoints.py::test_health_endpoints", "test_hybrid_search_phase3.py::TestHybridSearchPhase3::test_adaptive_fusion_weights", "test_hybrid_search_phase3.py::TestHybridSearchPhase3::test_bm25_search", "test_hybrid_search_phase3.py::TestHybridSearchPhase3::test_bm25_text_preprocessing", "test_hybrid_search_phase3.py::TestHybridSearchPhase3::test_query_expansion", "test_hybrid_search_phase3.py::TestHybridSearchPhase3::test_reciprocal_rank_fusion", "test_hybrid_search_phase3.py::TestHybridSearchPhase3::test_spell_correction", "test_hybrid_search_phase3.py::test_hybrid_search_integration", "test_hybrid_search_phase3.py::test_performance_benchmarks", "test_infrastructure.py::test_environment_variables", "test_infrastructure.py::test_error_handling", "test_infrastructure.py::test_file_system_access", "test_infrastructure.py::test_logging_system", "test_infrastructure.py::test_rate_limiting_infrastructure", "test_infrastructure.py::test_redis_connectivity", "test_openrouter.py::test_openrouter_integration", "test_performance.py::test_concurrent_performance", "test_performance.py::test_cost_tracker_performance", "test_performance.py::test_rate_limiter_performance", "test_performance.py::test_template_engine_performance", "tests/test_ai_service_integration.py::TestConversationMemoryIntegration::test_conversation_lifecycle", "tests/test_ai_service_integration.py::TestConversationMemoryIntegration::test_conversation_summarization", "tests/test_ai_service_integration.py::TestEndToEndIntegration::test_complete_rag_workflow", "tests/test_ai_service_integration.py::TestLLMManagerIntegration::test_anthropic_integration", "tests/test_ai_service_integration.py::TestLLMManagerIntegration::test_openai_integration", "tests/test_ai_service_integration.py::TestLLMManagerIntegration::test_provider_failover", "tests/test_ai_service_integration.py::TestPerformanceOptimization::test_performance_monitoring", "tests/test_ai_service_integration.py::TestPerformanceOptimization::test_performance_recommendations", "tests/test_ai_service_integration.py::TestQueryExpansionIntegration::test_llm_based_expansion", "tests/test_ai_service_integration.py::TestQueryExpansionIntegration::test_query_expansion_pipeline", "tests/test_ai_service_integration.py::TestRAGPipelineIntegration::test_document_processing_pipeline", "tests/test_ai_service_integration.py::TestRAGPipelineIntegration::test_rag_query_pipeline", "tests/test_ai_service_integration.py::TestResponseSynthesisIntegration::test_response_synthesis_pipeline", "tests/test_ai_service_integration.py::TestResponseSynthesisIntegration::test_response_validation_pipeline", "tests/test_ai_service_integration.py::TestSecurityIntegration::test_api_key_encryption", "tests/test_ai_service_integration.py::TestSecurityIntegration::test_input_validation", "tests/test_api_integration.py::TestFastAPIIntegration::test_chat_endpoint_success", "tests/test_api_integration.py::TestFastAPIIntegration::test_chat_endpoint_unauthorized", "tests/test_api_integration.py::TestFastAPIIntegration::test_conversations_endpoint", "tests/test_api_integration.py::TestFastAPIIntegration::test_delete_conversation_endpoint", "tests/test_api_integration.py::TestFastAPIIntegration::test_detailed_health_endpoint", "tests/test_api_integration.py::TestFastAPIIntegration::test_document_status_endpoint", "tests/test_api_integration.py::TestFastAPIIntegration::test_global_exception_handler", "tests/test_api_integration.py::TestFastAPIIntegration::test_health_endpoint", "tests/test_api_integration.py::TestFastAPIIntegration::test_rag_chat_endpoint_success", "tests/test_api_integration.py::TestFastAPIIntegration::test_readiness_endpoint", "tests/test_api_integration.py::TestFastAPIIntegration::test_search_documents_endpoint_success", "tests/test_api_integration.py::TestFastAPIIntegration::test_system_status_endpoint", "tests/test_api_integration.py::TestFastAPIIntegration::test_upload_document_endpoint_success", "tests/test_api_integration.py::TestFastAPIIntegration::test_upload_document_file_too_large", "tests/test_api_integration.py::TestFastAPIIntegration::test_usage_stats_endpoint", "tests/test_functional_validation.py::TestFunctionalRequirements::test_all_backend_endpoints_accessible", "tests/test_functional_validation.py::TestFunctionalRequirements::test_authentication_works_consistently", "tests/test_functional_validation.py::TestFunctionalRequirements::test_chat_functionality_with_rag", "tests/test_functional_validation.py::TestFunctionalRequirements::test_chat_functionality_without_rag", "tests/test_functional_validation.py::TestFunctionalRequirements::test_document_upload_processing_end_to_end", "tests/test_functional_validation.py::TestFunctionalRequirements::test_performance_requirements", "tests/test_functional_validation.py::TestFunctionalRequirements::test_search_returns_relevant_results", "tests/test_google_embeddings.py::TestBackwardCompatibility::test_model_config_compatibility", "tests/test_google_embeddings.py::TestBackwardCompatibility::test_openai_fallback", "tests/test_google_embeddings.py::TestDocumentProcessingIntegration::test_default_embedding_model", "tests/test_google_embeddings.py::TestDocumentProcessingIntegration::test_document_processing_with_google_embeddings", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_cache_functionality", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_google_batch_embeddings", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_google_embedding_generation", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_google_service_initialization", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_model_configurations", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_openai_embedding_generation", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_openai_service_initialization", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_service_availability", "tests/test_google_embeddings.py::TestGoogleEmbeddingService::test_text_validation", "tests/test_integration.py::TestAPIIntegration::test_chat_endpoint_integration", "tests/test_integration.py::TestAPIIntegration::test_document_upload_integration", "tests/test_integration.py::TestAPIIntegration::test_health_endpoint", "tests/test_integration.py::TestAPIIntegration::test_rag_chat_endpoint_integration", "tests/test_integration.py::TestAPIIntegration::test_search_documents_integration", "tests/test_integration.py::TestConcurrencyIntegration::test_concurrent_queries", "tests/test_integration.py::TestDataFlowIntegration::test_conversation_memory_flow", "tests/test_integration.py::TestDataFlowIntegration::test_query_expansion_flow", "tests/test_integration.py::TestErrorHandlingIntegration::test_llm_error_handling", "tests/test_integration.py::TestErrorHandlingIntegration::test_rag_error_handling", "tests/test_integration.py::TestPerformanceIntegration::test_cache_integration", "tests/test_integration.py::TestPerformanceIntegration::test_performance_monitoring_integration", "tests/test_integration.py::TestRAGPipelineIntegration::test_conversation_context_integration", "tests/test_integration.py::TestRAGPipelineIntegration::test_document_to_query_workflow", "tests/test_integration.py::TestSecurityIntegration::test_rate_limiting_integration", "tests/test_integration.py::TestSecurityIntegration::test_request_validation_integration", "tests/test_integration.py::TestSecurityIntegration::test_threat_detection_integration", "tests/test_llm_core.py::TestCostTracker::test_cost_breakdown", "tests/test_llm_core.py::TestCostTracker::test_cost_calculation", "tests/test_llm_core.py::TestCostTracker::test_cost_limits_check", "tests/test_llm_core.py::TestCostTracker::test_usage_tracking", "tests/test_llm_core.py::TestIntegration::test_template_and_cost_integration", "tests/test_llm_core.py::TestLLMManager::test_available_providers", "tests/test_llm_core.py::TestLLMManager::test_openai_response_generation", "tests/test_llm_core.py::TestLLMManager::test_provider_initialization", "tests/test_llm_core.py::TestLLMManager::test_provider_status", "tests/test_llm_core.py::TestRateLimiter::test_rate_limit_allow", "tests/test_llm_core.py::TestRateLimiter::test_rate_limit_exceed", "tests/test_llm_core.py::TestRateLimiter::test_rate_limit_reset", "tests/test_llm_core.py::TestRateLimiter::test_rate_limit_status", "tests/test_llm_core.py::TestTemplateEngine::test_conditional_rendering", "tests/test_llm_core.py::TestTemplateEngine::test_extract_variables", "tests/test_llm_core.py::TestTemplateEngine::test_helper_functions", "tests/test_llm_core.py::TestTemplateEngine::test_loop_rendering", "tests/test_llm_core.py::TestTemplateEngine::test_missing_variables", "tests/test_llm_core.py::TestTemplateEngine::test_simple_variable_substitution", "tests/test_llm_core.py::TestTemplateEngine::test_template_validation", "tests/test_llm_manager.py::TestLLMManager::test_concurrent_requests", "tests/test_llm_manager.py::TestLLMManager::test_cost_calculation_anthropic", "tests/test_llm_manager.py::TestLLMManager::test_cost_calculation_openai", "tests/test_llm_manager.py::TestLLMManager::test_error_handling_invalid_provider", "tests/test_llm_manager.py::TestLLMManager::test_generate_response_all_providers_fail", "tests/test_llm_manager.py::TestLLMManager::test_generate_response_success", "tests/test_llm_manager.py::TestLLMManager::test_generate_response_with_fallback", "tests/test_llm_manager.py::TestLLMManager::test_model_configuration_validation", "tests/test_llm_manager.py::TestLLMManager::test_provider_availability_check", "tests/test_llm_manager.py::TestLLMManager::test_provider_config_creation", "tests/test_llm_manager.py::TestLLMManager::test_provider_initialization", "tests/test_llm_manager.py::TestLLMManager::test_provider_status_check", "tests/test_llm_manager.py::TestLLMManager::test_rate_limiting_integration", "tests/test_llm_manager.py::TestLLMManager::test_response_metadata_structure", "tests/test_llm_manager.py::TestLLMManager::test_streaming_response_handling", "tests/test_llm_manager.py::TestLLMManager::test_template_variable_handling", "tests/test_llm_manager.py::TestLLMManager::test_timeout_handling", "tests/test_main.py::test_placeholder", "tests/test_performance_validation.py::TestPerformanceRequirements::test_chat_response_time_under_2_seconds", "tests/test_performance_validation.py::TestPerformanceRequirements::test_concurrent_user_handling", "tests/test_performance_validation.py::TestPerformanceRequirements::test_document_processing_under_30_seconds", "tests/test_performance_validation.py::TestPerformanceRequirements::test_document_search_under_500ms", "tests/test_performance_validation.py::TestPerformanceRequirements::test_error_handling_under_load", "tests/test_performance_validation.py::TestPerformanceRequirements::test_system_resource_efficiency", "tests/test_quality_validation.py::TestQualityRequirements::test_api_documentation_available", "tests/test_quality_validation.py::TestQualityRequirements::test_configuration_validation", "tests/test_quality_validation.py::TestQualityRequirements::test_critical_paths_coverage", "tests/test_quality_validation.py::TestQualityRequirements::test_data_validation_comprehensive", "tests/test_quality_validation.py::TestQualityRequirements::test_error_handling_edge_cases", "tests/test_quality_validation.py::TestQualityRequirements::test_exception_handling_consistency", "tests/test_quality_validation.py::TestQualityRequirements::test_logging_system_functional", "tests/test_quality_validation.py::TestQualityRequirements::test_monitoring_endpoints_available", "tests/test_quality_validation.py::TestQualityRequirements::test_security_headers_present"]