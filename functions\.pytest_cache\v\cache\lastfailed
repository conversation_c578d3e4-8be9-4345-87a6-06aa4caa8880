{"tests/test_openrouter_integration.py": true, "tests/test_prompt_generation.py": true, "tests/test_rag_pipeline.py": true, "test_core_logic.py": true, "test_rag_pipeline.py": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_health_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_detailed_health_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_readiness_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_chat_endpoint_success": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_chat_endpoint_unauthorized": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_rag_chat_endpoint_success": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_upload_document_endpoint_success": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_upload_document_file_too_large": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_search_documents_endpoint_success": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_system_status_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_usage_stats_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_conversations_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_delete_conversation_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_document_status_endpoint": true, "tests/test_api_integration.py::TestFastAPIIntegration::test_global_exception_handler": true, "tests/test_functional_validation.py::TestFunctionalRequirements::test_all_backend_endpoints_accessible": true, "tests/test_functional_validation.py::TestFunctionalRequirements::test_document_upload_processing_end_to_end": true, "tests/test_functional_validation.py::TestFunctionalRequirements::test_chat_functionality_without_rag": true, "tests/test_functional_validation.py::TestFunctionalRequirements::test_chat_functionality_with_rag": true, "tests/test_functional_validation.py::TestFunctionalRequirements::test_search_returns_relevant_results": true, "tests/test_functional_validation.py::TestFunctionalRequirements::test_authentication_works_consistently": true, "tests/test_functional_validation.py::TestFunctionalRequirements::test_performance_requirements": true, "tests/test_integration.py::TestAPIIntegration::test_health_endpoint": true, "tests/test_integration.py::TestAPIIntegration::test_chat_endpoint_integration": true, "tests/test_integration.py::TestAPIIntegration::test_rag_chat_endpoint_integration": true, "tests/test_integration.py::TestAPIIntegration::test_document_upload_integration": true, "tests/test_integration.py::TestAPIIntegration::test_search_documents_integration": true, "tests/test_integration.py::TestRAGPipelineIntegration::test_document_to_query_workflow": true, "tests/test_integration.py::TestRAGPipelineIntegration::test_conversation_context_integration": true, "tests/test_integration.py::TestPerformanceIntegration::test_performance_monitoring_integration": true, "tests/test_integration.py::TestPerformanceIntegration::test_cache_integration": true, "tests/test_integration.py::TestSecurityIntegration::test_request_validation_integration": true, "tests/test_integration.py::TestSecurityIntegration::test_threat_detection_integration": true, "tests/test_integration.py::TestSecurityIntegration::test_rate_limiting_integration": true, "tests/test_integration.py::TestErrorHandlingIntegration::test_llm_error_handling": true, "tests/test_integration.py::TestErrorHandlingIntegration::test_rag_error_handling": true, "tests/test_integration.py::TestDataFlowIntegration::test_conversation_memory_flow": true, "tests/test_integration.py::TestDataFlowIntegration::test_query_expansion_flow": true, "tests/test_integration.py::TestConcurrencyIntegration::test_concurrent_queries": true, "tests/test_performance_validation.py::TestPerformanceRequirements::test_chat_response_time_under_2_seconds": true, "tests/test_performance_validation.py::TestPerformanceRequirements::test_document_search_under_500ms": true, "tests/test_performance_validation.py::TestPerformanceRequirements::test_document_processing_under_30_seconds": true, "tests/test_performance_validation.py::TestPerformanceRequirements::test_concurrent_user_handling": true, "tests/test_performance_validation.py::TestPerformanceRequirements::test_system_resource_efficiency": true, "tests/test_performance_validation.py::TestPerformanceRequirements::test_error_handling_under_load": true, "tests/test_quality_validation.py::TestQualityRequirements::test_critical_paths_coverage": true, "tests/test_quality_validation.py::TestQualityRequirements::test_error_handling_edge_cases": true, "tests/test_quality_validation.py::TestQualityRequirements::test_logging_system_functional": true, "tests/test_quality_validation.py::TestQualityRequirements::test_monitoring_endpoints_available": true, "tests/test_quality_validation.py::TestQualityRequirements::test_configuration_validation": true, "tests/test_quality_validation.py::TestQualityRequirements::test_exception_handling_consistency": true, "tests/test_quality_validation.py::TestQualityRequirements::test_security_headers_present": true, "tests/test_quality_validation.py::TestQualityRequirements::test_api_documentation_available": true, "tests/test_quality_validation.py::TestQualityRequirements::test_data_validation_comprehensive": true, "tests/test_ai_service_integration.py": true, "tests/test_api_integration.py": true, "tests/test_functional_validation.py": true, "tests/test_hybrid_search_integration.py": true, "tests/test_integration.py": true, "tests/test_performance_validation.py": true, "tests/test_quality_validation.py": true, "tests/test_relevance_validation.py": true, "tests/test_search_performance.py": true}